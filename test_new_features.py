#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新功能的脚本
测试模板图片显示和右键删除功能
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

def test_features():
    """测试新功能"""
    print("=== 功能优化测试 ===")
    print("1. 模板图片显示功能")
    print("   - 在工作流步骤显示中添加了 [模板: xxx] 信息")
    print("   - 双击编辑步骤时会显示模板图片预览")
    print()
    
    print("2. 右键删除功能")
    print("   - 在脚本工作流文本区域右键点击可显示菜单")
    print("   - 支持编辑和删除选中的步骤")
    print("   - 支持清空所有步骤")
    print()
    
    print("测试步骤：")
    print("1. 运行 automation_ui.py")
    print("2. 切换到'脚本工作流'标签")
    print("3. 添加一些动作到工作流")
    print("4. 观察步骤显示格式是否包含模板信息")
    print("5. 双击步骤行，检查是否显示模板图片预览")
    print("6. 右键点击步骤行，检查右键菜单功能")
    print("7. 测试删除步骤功能")
    print()
    
    # 检查依赖
    try:
        import cv2
        print("✓ OpenCV 可用")
    except ImportError:
        print("✗ OpenCV 不可用")
    
    try:
        from PIL import Image, ImageTk
        print("✓ PIL/Pillow 可用")
    except ImportError:
        print("✗ PIL/Pillow 不可用 - 模板图片预览功能可能无法正常工作")
    
    try:
        import pynput
        print("✓ pynput 可用")
    except ImportError:
        print("✗ pynput 不可用 - 录制功能可能无法正常工作")
    
    print()
    print("=== 测试完成 ===")

if __name__ == "__main__":
    test_features()
